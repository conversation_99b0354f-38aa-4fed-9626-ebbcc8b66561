import { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useMoniteToken } from '@/libs/auth/hooks/useMoniteToken';
import { Button } from '@/components/atoms/Button/Button';
import { Loader } from '@/libs/ui/Loader/Loader';
import { useAccountStore } from '@/apps/shop/stores/useAccountStore';
import { INVOICES_CONSTANTS } from './constants';

export const Invoices = () => {
  const { t } = useTranslation();
  const moniteContainerRef = useRef<HTMLDivElement>(null);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [moniteDropin, setMoniteDropin] = useState<any>(null);
  const [isMoniteLoaded, setIsMoniteLoaded] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  const activeClinic = useAccountStore((state) => state.activeClinic);

  const { token, isLoading, hasError, fetchToken, error } = useMoniteToken({
    clinicId: activeClinic?.id || '',
  });

  // Load Monite SDK dynamically
  useEffect(() => {
    let isMounted = true;

    const loadMoniteSDK = async () => {
      try {
        // @ts-expect-error - Dynamic import for Monite SDK
        const { MoniteDropin } = await import('@monite/sdk-drop-in');

        if (isMounted) {
          setMoniteDropin(() => MoniteDropin);
          setIsMoniteLoaded(true);
        }
      } catch (error) {
        console.error('Failed to load Monite SDK:', error);
        if (isMounted) {
          setIsMoniteLoaded(false);
        }
      }
    };

    loadMoniteSDK();

    return () => {
      isMounted = false;
    };
  }, []);

  // Initialize Monite when token is available
  useEffect(() => {
    if (token && moniteDropin && moniteContainerRef.current && !isInitialized) {
      try {
        const dropinConfig = {
          entityId: token.entityId,
          apiUrl: INVOICES_CONSTANTS.MONITE_API_URL,
          fetchToken: async () => {
            // Return the token data in the exact format from our API response
            // This matches what Monite expects based on the documentation
            return {
              access_token: token.accessToken,
              token_type: token.tokenType,
              expires_in: token.expiresIn,
            };
          },
          theme: INVOICES_CONSTANTS.MONITE_THEME,
          locale: INVOICES_CONSTANTS.MONITE_LOCALE,
        };

        // Clear any existing content
        if (moniteContainerRef.current) {
          moniteContainerRef.current.innerHTML = '';
        }

        const dropin = new moniteDropin(dropinConfig);

        // Create and mount the payables component
        // According to Monite docs, this is the correct way to mount components
        const payablesComponent = dropin.create('payables');
        payablesComponent.mount(moniteContainerRef.current);

        setIsInitialized(true);
        console.log('Monite payables component mounted successfully', {
          entityId: token.entityId,
          apiUrl: INVOICES_CONSTANTS.MONITE_API_URL,
        });
      } catch (error) {
        console.error('Failed to initialize Monite:', error);
        setIsInitialized(false);
      }
    }
  }, [token, moniteDropin, isInitialized]);

  // Cleanup effect
  useEffect(() => {
    return () => {
      if (moniteContainerRef.current) {
        moniteContainerRef.current.innerHTML = '';
      }
    };
  }, []);

  const handleRetry = () => {
    setIsInitialized(false);
    fetchToken();
  };

  // Show message if no clinic is selected
  if (!activeClinic?.id) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <div className="max-w-md text-center">
          <h3 className="mb-2 text-lg font-semibold text-gray-600">
            {t('client.invoices.noClinic.title')}
          </h3>
          <p className="mb-4 text-gray-600">
            {t('client.invoices.noClinic.message')}
          </p>
        </div>
      </div>
    );
  }

  if (isLoading || !isMoniteLoaded) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <div className="text-center">
          <Loader size="3rem" />
          <p className="mt-4 text-gray-600">
            {isLoading ? t('client.invoices.loading') : 'Loading Monite SDK...'}
          </p>
        </div>
      </div>
    );
  }

  if (hasError || error) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <div className="max-w-md text-center">
          <h3 className="mb-2 text-lg font-semibold text-red-600">
            {t('client.invoices.error.title')}
          </h3>
          <p className="mb-4 text-gray-600">
            {error || t('client.invoices.error.message')}
          </p>
          <Button onClick={handleRetry} variant="primary">
            {t('client.invoices.error.retry')}
          </Button>
        </div>
      </div>
    );
  }

  if (!token) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <div className="text-center">
          <Button onClick={handleRetry} variant="primary">
            {t('client.invoices.load')}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          {t('client.invoices.title')}
        </h1>
        <p className="mt-2 text-gray-600">{t('client.invoices.description')}</p>
      </div>

      <div className="rounded-lg bg-white shadow">
        <div ref={moniteContainerRef} className="min-h-[600px]" />
      </div>
    </div>
  );
};
