export const INVOICES_CONSTANTS = {
  // Monite API configuration - from environment variables
  MONITE_API_URL:
    import.meta.env.VITE_MONITE_API_URL || 'https://api.sandbox.monite.com/v1',

  // Theme configuration aligned with Highfive app design system
  MONITE_THEME: {
    borderRadius: 8, // Matches app's border-radius: 0.5rem
    spacing: 16, // Matches app's spacing patterns
    colors: {
      // Primary: Yellow theme from app's primary buttons
      primary: '#F1C40F',
      // Secondary: Light blue theme from app's secondary buttons
      secondary: '#74B9FF',
      // Neutral: Dark colors from app's text
      neutral: '#6C7B7F',
      // Info: Light blue from app's info elements
      info: '#0984E3',
      // Success: Green from app's success states
      success: '#00B894',
      // Warning: Yellow from app's warning states
      warning: '#FDCB6E',
      // Error: Red from app's error states
      error: '#E17055',
      // Background: White from app's backgrounds
      background: '#FFFFFF',
      // Text: Dark from app's text colors
      text: '#2D3436',
    },
  },

  // Localization aligned with Highfive app terminology
  MONITE_LOCALE: {
    code: 'en-US',
    messages: {
      // Main navigation and sections
      Payables: 'Invoices',
      Counterparts: 'Vendors',
      Sales: 'Sales',
      Invoices: 'Invoices',

      // Form labels and actions
      'Counterpart Name': 'Vendor Name',
      'Create New': 'Create New',
      'Add New': 'Add New',
      Edit: 'Edit',
      Delete: 'Delete',
      Save: 'Save',
      Cancel: 'Cancel',

      // Status and states
      Draft: 'Draft',
      Sent: 'Sent',
      Paid: 'Paid',
      Overdue: 'Overdue',
      Cancelled: 'Cancelled',

      // Common terms
      Amount: 'Amount',
      Date: 'Date',
      'Due Date': 'Due Date',
      Status: 'Status',
      Description: 'Description',
      Notes: 'Notes',
    },
  },
} as const;
