import type { VendorType } from '@/types';
import { Badge } from '@/libs/ui/Badge/Badge';
import { CONNECTION } from '@/apps/shop/constants';

interface VendorConnectionStatusProps {
  status: VendorType['status'];
}
export const VendorConnectionStatus = ({
  status,
}: VendorConnectionStatusProps) => {
  const statusConfigs: Record<VendorType['status'], string> = {
    [CONNECTION.CONNECTED]: 'bg-[#89BF77] text-white',
    [CONNECTION.CONNECTING]: 'bg-[#B6F5F9] text-[#344054]',
    [CONNECTION.DISCONNECTED]: '',
  };

  const style = statusConfigs[status];

  if (style) {
    return <Badge className={style}>{status}</Badge>;
  }

  return null;
};
